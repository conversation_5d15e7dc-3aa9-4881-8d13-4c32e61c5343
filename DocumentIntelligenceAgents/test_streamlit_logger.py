#!/usr/bin/env python3
"""
Test script to demonstrate Streamlit logger integration
"""
import streamlit as st
import time
import asyncio
from utils.logger import Agent<PERSON><PERSON><PERSON>

def test_streamlit_logger():
    """Test the logger with Streamlit integration"""
    st.title("🧪 AgentLogger Streamlit Integration Test")
    
    # Create logger container
    st.subheader("📋 Logger Output")
    logger_container = st.container()
    
    # Create logger
    logger = AgentLogger("TestAgent", use_streamlit=True)
    logger.set_streamlit_container(logger_container)
    
    if st.button("🚀 Test Logger Operations"):
        with st.spinner("Running logger tests..."):
            # Test basic logging
            logger.info("Starting logger test")
            time.sleep(0.5)
            
            # Test operation logging
            logger.start_operation(
                "Test Operation",
                "Testing Streamlit integration",
                {"test_mode": True, "iterations": 3}
            )
            
            time.sleep(1)
            
            # Test step logging
            for i in range(3):
                logger.log_step(
                    f"Processing Step {i+1}",
                    {"step_number": i+1, "data_processed": f"{(i+1)*100} items"},
                    "IN_PROGRESS"
                )
                time.sleep(0.5)
                
                logger.log_step(
                    f"Step {i+1} Complete",
                    {"result": "success", "time_taken": f"{0.5}s"},
                    "COMPLETED"
                )
                time.sleep(0.3)
            
            # Test different message types
            logger.success("All steps completed successfully!")
            logger.warning("This is a test warning message")
            logger.info("Additional information logged")
            
            # Test metrics
            logger.metrics({
                "total_steps": 3,
                "success_rate": "100%",
                "avg_step_time": "0.8s",
                "total_items_processed": 300
            })
            
            # Complete operation
            logger.complete_operation(
                success=True,
                summary={
                    "steps_completed": 3,
                    "total_time": "3.5s",
                    "items_processed": 300,
                    "success_rate": "100%"
                }
            )
            
            logger.success("Logger test completed!")
        
        st.success("✅ Logger test completed! Check the output above.")
    
    # Show logger configuration
    with st.expander("🔧 Logger Configuration"):
        st.write("**Logger Settings:**")
        st.write(f"- Agent Name: TestAgent")
        st.write(f"- Streamlit Integration: Enabled")
        st.write(f"- Log Level: INFO")
        st.write(f"- Container Set: {'Yes' if logger.streamlit_container is not None else 'No'}")

async def test_async_operations():
    """Test async operations with logger"""
    st.subheader("🔄 Async Operations Test")
    
    async_container = st.container()
    logger = AgentLogger("AsyncAgent", use_streamlit=True)
    logger.set_streamlit_container(async_container)
    
    if st.button("🚀 Test Async Operations"):
        with st.spinner("Running async operations..."):
            # Simulate async document processing
            logger.start_operation(
                "Async Document Processing",
                "Simulating parallel document processing",
                {"documents": 5, "parallel_workers": 3}
            )
            
            # Simulate parallel processing
            async def process_document(doc_id):
                logger.log_step(
                    f"Processing Document {doc_id}",
                    {"document_id": doc_id, "status": "starting"},
                    "IN_PROGRESS"
                )
                await asyncio.sleep(1)  # Simulate processing time
                
                logger.log_step(
                    f"Document {doc_id} Complete",
                    {"document_id": doc_id, "pages_processed": doc_id * 2},
                    "COMPLETED"
                )
                return f"doc_{doc_id}_result"
            
            # Process documents in parallel
            tasks = [process_document(i+1) for i in range(5)]
            results = await asyncio.gather(*tasks)
            
            logger.complete_operation(
                success=True,
                summary={
                    "documents_processed": len(results),
                    "total_pages": sum(range(2, 12, 2)),
                    "processing_mode": "parallel"
                }
            )
        
        st.success("✅ Async operations test completed!")

def main():
    st.set_page_config(
        page_title="Logger Test",
        page_icon="🧪",
        layout="wide"
    )
    
    # Test synchronous operations
    test_streamlit_logger()
    
    st.divider()
    
    # Test asynchronous operations
    if st.button("🔄 Run Async Test"):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(test_async_operations())
        loop.close()
    
    # Instructions
    with st.sidebar:
        st.header("📖 Instructions")
        st.write("""
        This test demonstrates the AgentLogger integration with Streamlit:
        
        1. **Basic Logging**: Click 'Test Logger Operations' to see basic logging functionality
        2. **Async Operations**: Click 'Run Async Test' to see async operation logging
        3. **Real-time Updates**: Watch the logs appear in real-time as operations progress
        4. **Different Message Types**: See info, success, warning, and error messages
        5. **Operation Tracking**: See how operations and steps are tracked
        
        The logger now properly integrates with Streamlit containers for better visibility.
        """)

if __name__ == "__main__":
    main()
