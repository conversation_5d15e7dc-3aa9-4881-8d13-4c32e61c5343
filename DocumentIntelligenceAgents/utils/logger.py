import logging
import sys
import time
from datetime import datetime
from typing import Optional, Dict, Any, List
import streamlit as st

class AgentLogger:
    """Enhanced logging system for tracking agent operations with Streamlit integration"""

    def __init__(self, agent_name: str, log_level: str = "INFO", use_streamlit: bool = True):
        self.agent_name = agent_name
        self.use_streamlit = use_streamlit
        self.logger = logging.getLogger(f"Agent.{agent_name}")
        self.logger.setLevel(getattr(logging, log_level.upper()))

        # Step tracking
        self.current_step = None
        self.step_start_time = None
        self.step_history = []
        self.operation_metrics = {}

        # Streamlit containers for real-time updates
        self.streamlit_container = None
        self.current_expander = None

        # Create console handler if not exists
        if not self.logger.handlers:
            handler = logging.StreamHandler(sys.stdout)
            handler.setLevel(getattr(logging, log_level.upper()))

            # Create formatter
            formatter = logging.Formatter(
                f'%(asctime)s | {agent_name} | %(levelname)s | %(message)s',
                datefmt='%H:%M:%S'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)

    def set_streamlit_container(self, container):
        """Set the Streamlit container for displaying logs"""
        self.streamlit_container = container

    def _log_to_streamlit(self, message: str, level: str = "info"):
        """Log message to Streamlit if available and container is set"""
        if self.use_streamlit and self.streamlit_container is not None:
            try:
                with self.streamlit_container:
                    if level == "info":
                        st.info(message)
                    elif level == "success":
                        st.success(message)
                    elif level == "warning":
                        st.warning(message)
                    elif level == "error":
                        st.error(message)
                    else:
                        st.write(message)
            except Exception:
                pass  # Fallback silently if Streamlit not available

    def _print_and_log(self, console_msg: str, log_msg: str, log_level: str = "info", streamlit_level: str = "info"):
        """Print to console, log to Python logger, and display in Streamlit"""
        # Always print to console for direct script execution
        print(console_msg)

        # Log to Python logger
        getattr(self.logger, log_level)(log_msg)

        # Display in Streamlit
        self._log_to_streamlit(console_msg, streamlit_level)
    

    def start_operation(self, operation_name: str, description: str = "", context: Optional[Dict] = None):
        """Start a new operation with detailed logging"""
        self.current_step = operation_name
        self.step_start_time = time.time()

        # Create console message
        console_msg = f"\n{'='*60}\n🚀 [{self.agent_name}] STARTING OPERATION: {operation_name}"
        if description:
            console_msg += f"\n📝 Description: {description}"
        if context:
            console_msg += f"\n📊 Context: {context}"
        console_msg += f"\n{'='*60}"

        # Create Streamlit message
        streamlit_msg = f"🚀 **{self.agent_name}** - Starting: {operation_name}"
        if description:
            streamlit_msg += f"\n\n📝 {description}"

        # Log using unified method
        self._print_and_log(console_msg, f"OPERATION STARTED: {operation_name}", "info", "info")

        # Create Streamlit expander if container is available
        if self.use_streamlit and self.streamlit_container is not None:
            try:
                with self.streamlit_container:
                    self.current_expander = st.expander(f"🔄 {self.agent_name} - {operation_name}", expanded=True)
                    with self.current_expander:
                        st.write(f"**Operation:** {operation_name}")
                        if description:
                            st.write(f"**Description:** {description}")
                        if context:
                            st.json(context)
                        st.write(f"**Started at:** {datetime.now().strftime('%H:%M:%S')}")
            except Exception:
                pass  # Fallback if Streamlit not available
    
    def log_step(self, step_name: str, details: Optional[Dict] = None, status: str = "IN_PROGRESS"):
        """Log a detailed step within an operation"""
        timestamp = datetime.now().strftime('%H:%M:%S')

        status_emoji = {
            "IN_PROGRESS": "⚙️",
            "COMPLETED": "✅",
            "FAILED": "❌",
            "WARNING": "⚠️",
            "INFO": "🔍"
        }

        emoji = status_emoji.get(status, "🔍")

        # Create console message
        console_msg = f"{emoji} [{self.agent_name}] STEP: {step_name}"
        if details:
            for key, value in details.items():
                console_msg += f"\n   • {key}: {value}"

        # Determine Streamlit level based on status
        streamlit_level = "info"
        if status == "COMPLETED":
            streamlit_level = "success"
        elif status == "FAILED":
            streamlit_level = "error"
        elif status == "WARNING":
            streamlit_level = "warning"

        # Log using unified method
        self._print_and_log(console_msg, f"STEP: {step_name} - {status}", "info", streamlit_level)

        # Store step history
        step_record = {
            "step_name": step_name,
            "timestamp": timestamp,
            "status": status,
            "details": details or {}
        }
        self.step_history.append(step_record)

        # Update Streamlit expander if available
        if self.current_expander is not None:
            try:
                with self.current_expander:
                    st.write(f"{emoji} **{step_name}** - {status}")
                    if details:
                        for key, value in details.items():
                            st.write(f"   • {key}: {value}")
            except Exception:
                pass
    
    def complete_operation(self, success: bool = True, summary: Optional[Dict] = None):
        """Complete the current operation with summary"""
        if self.current_step and self.step_start_time:
            duration = time.time() - self.step_start_time

            status = "COMPLETED" if success else "FAILED"
            emoji = "✅" if success else "❌"

            # Create console message
            console_msg = f"\n{emoji} [{self.agent_name}] OPERATION {status}: {self.current_step}"
            console_msg += f"\n⏱️  Duration: {duration:.2f} seconds"
            if summary:
                console_msg += f"\n📊 Summary:"
                for key, value in summary.items():
                    console_msg += f"\n   • {key}: {value}"
            console_msg += f"\n{'='*60}\n"

            # Determine Streamlit level
            streamlit_level = "success" if success else "error"

            # Log using unified method
            self._print_and_log(console_msg, f"OPERATION {status}: {self.current_step} - Duration: {duration:.2f}s", "info", streamlit_level)

            # Store operation metrics
            self.operation_metrics[self.current_step] = {
                "duration": duration,
                "success": success,
                "summary": summary or {},
                "steps_count": len([s for s in self.step_history if s["step_name"].startswith(self.current_step)])
            }

            # Update Streamlit expander if available
            if self.current_expander is not None:
                try:
                    with self.current_expander:
                        st.write(f"{emoji} **Operation {status}**")
                        st.write(f"⏱️ Duration: {duration:.2f} seconds")
                        if summary:
                            st.write("📊 **Summary:**")
                            for key, value in summary.items():
                                st.write(f"   • {key}: {value}")
                except Exception:
                    pass

            # Reset current operation
            self.current_step = None
            self.step_start_time = None
            self.current_expander = None

    def info(self, message: str, extra_data: Optional[dict] = None):
        """Log info message"""
        console_msg = f"🔍 [{self.agent_name}] {message}"
        if extra_data:
            console_msg += f"\n   📊 {extra_data}"

        log_msg = message
        if extra_data:
            log_msg += f" | Data: {extra_data}"

        self._print_and_log(console_msg, log_msg, "info", "info")

    def success(self, message: str, extra_data: Optional[dict] = None):
        """Log success message"""
        console_msg = f"✅ [{self.agent_name}] {message}"
        if extra_data:
            console_msg += f"\n   📊 {extra_data}"

        log_msg = message
        if extra_data:
            log_msg += f" | Data: {extra_data}"

        self._print_and_log(console_msg, log_msg, "info", "success")

    def warning(self, message: str, extra_data: Optional[dict] = None):
        """Log warning message"""
        console_msg = f"⚠️  [{self.agent_name}] {message}"
        if extra_data:
            console_msg += f"\n   📊 {extra_data}"

        log_msg = message
        if extra_data:
            log_msg += f" | Data: {extra_data}"

        self._print_and_log(console_msg, log_msg, "warning", "warning")

    def error(self, message: str, extra_data: Optional[dict] = None):
        """Log error message"""
        console_msg = f"❌ [{self.agent_name}] {message}"
        if extra_data:
            console_msg += f"\n   📊 {extra_data}"

        log_msg = message
        if extra_data:
            log_msg += f" | Data: {extra_data}"

        self._print_and_log(console_msg, log_msg, "error", "error")

    def step(self, step_name: str, status: str = "STARTING", extra_data: Optional[dict] = None):
        """Log processing step (legacy method)"""
        self.log_step(step_name, extra_data, status)

    def metrics(self, metrics: dict):
        """Log performance metrics"""
        console_msg = f"📊 [{self.agent_name}] METRICS:"
        for key, value in metrics.items():
            console_msg += f"\n   • {key}: {value}"

        self._print_and_log(console_msg, f"METRICS: {metrics}", "info", "info")
    
    def get_operation_summary(self) -> Dict[str, Any]:
        """Get summary of all operations performed by this agent"""
        return {
            "agent_name": self.agent_name,
            "total_operations": len(self.operation_metrics),
            "total_steps": len(self.step_history),
            "operations": self.operation_metrics,
            "recent_steps": self.step_history[-10:] if self.step_history else []
        }
    
    def display_agent_status(self, status_container):
        """Display real-time agent status in Streamlit"""
        if self.use_streamlit:
            try:
                with status_container:
                    if self.current_step:
                        st.write(f"🔄 **{self.agent_name}**")
                        st.write(f"Current: {self.current_step}")
                        if self.step_start_time:
                            elapsed = time.time() - self.step_start_time
                            st.write(f"Running: {elapsed:.1f}s")
                    else:
                        st.write(f"⏳ **{self.agent_name}**")
                        st.write("Ready")
            except:
                pass  # Fallback if Streamlit not available
