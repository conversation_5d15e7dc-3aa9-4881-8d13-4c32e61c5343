import streamlit as st
import json
import asyncio
import traceback
import time
from typing import Dict, Any, Optional
import base64
from io import BytesIO

from agents.document_processor import DocumentProcessorAgent
from agents.layout_analyzer import LayoutAnalyzerAgent
from agents.data_extractor import DataExtractorAgent
from agents.aggregator import AggregatorAgent
from agents.validator import ValidatorAgent
from agents.output_formatter import OutputFormatterAgent
from utils.file_handler import FileHandler
from utils.schema_validator import SchemaValidator
from utils.agent_status_tracker import AgentStatusTracker
from config.agent_config import AgentConfig

# Page configuration
st.set_page_config(
    page_title="Portfolio Document Extractor",
    page_icon="📊",
    layout="wide"
)

# Initialize session state
if 'processing_complete' not in st.session_state:
    st.session_state.processing_complete = False
if 'extracted_data' not in st.session_state:
    st.session_state.extracted_data = None
if 'upload_key' not in st.session_state:
    st.session_state.upload_key = 0

def reset_session():
    """Reset session state for new upload"""
    st.session_state.processing_complete = False
    st.session_state.extracted_data = None
    st.session_state.upload_key += 1

def display_file_preview(uploaded_file):
    """Display preview of uploaded file"""
    try:
        file_type = uploaded_file.type
        
        if file_type.startswith('image/'):
            st.image(uploaded_file, caption=f"Preview: {uploaded_file.name}")
        elif file_type == 'application/pdf':
            st.info(f"📄 PDF Document: {uploaded_file.name}")
            st.write(f"Size: {len(uploaded_file.getvalue())} bytes")
        elif file_type in ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']:
            st.info(f"📝 Word Document: {uploaded_file.name}")
            st.write(f"Size: {len(uploaded_file.getvalue())} bytes")
        else:
            st.info(f"📎 Document: {uploaded_file.name}")
            st.write(f"Type: {file_type}")
            st.write(f"Size: {len(uploaded_file.getvalue())} bytes")
            
    except Exception as e:
        st.error(f"Error displaying preview: {str(e)}")

async def process_document_pipeline(uploaded_file) -> Optional[Dict[str, Any]]:
    """Process document through the multi-agent pipeline with parallel processing"""
    try:
        # Initialize agents
        config = AgentConfig()
        file_handler = FileHandler()

        # Create agents
        doc_processor = DocumentProcessorAgent(config)
        layout_analyzer = LayoutAnalyzerAgent(config)
        data_extractor = DataExtractorAgent(config)
        aggregator = AggregatorAgent(config)
        validator = ValidatorAgent(config)
        output_formatter = OutputFormatterAgent(config)

        # Create progress tracking containers
        progress_container = st.container()
        with progress_container:
            st.subheader("🔄 Processing Pipeline Status")

            # Initialize status indicators
            status_cols = st.columns(6)
            status_indicators = {}
            timing_metrics = {}

            agent_names = [
                "Document Processor", "Layout Analyzer", "Data Extractor",
                "Aggregator", "Validator", "Output Formatter"
            ]

            for i, agent_name in enumerate(agent_names):
                with status_cols[i]:
                    status_indicators[agent_name] = st.empty()
                    status_indicators[agent_name].info(f"⏳ {agent_name}\nWaiting...")
                    timing_metrics[agent_name] = {"start": None, "end": None}

        # Create logging container for detailed agent output
        logging_container = st.container()
        with logging_container:
            st.subheader("📋 Agent Processing Logs")
            agent_log_container = st.empty()

        # Set up agent loggers with Streamlit containers
        doc_processor.logger.set_streamlit_container(agent_log_container)
        layout_analyzer.logger.set_streamlit_container(agent_log_container)
        data_extractor.logger.set_streamlit_container(agent_log_container)
        aggregator.logger.set_streamlit_container(agent_log_container)
        validator.logger.set_streamlit_container(agent_log_container)
        output_formatter.logger.set_streamlit_container(agent_log_container)
        
        # Process file
        file_content = file_handler.process_uploaded_file(uploaded_file)
        
        # Stage 1: Document Processing and Chunking
        timing_metrics["Document Processor"]["start"] = time.time()
        status_indicators["Document Processor"].warning("🔄 Document Processor\nProcessing...")
        processed_chunks = await doc_processor.process_document(file_content)
        timing_metrics["Document Processor"]["end"] = time.time()
        
        if not processed_chunks:
            status_indicators["Document Processor"].error("❌ Document Processor\nFailed")
            st.error("Failed to process document chunks")
            return None
        
        doc_time = timing_metrics["Document Processor"]["end"] - timing_metrics["Document Processor"]["start"]
        status_indicators["Document Processor"].success(f"✅ Document Processor\n{len(processed_chunks)} chunks ({doc_time:.1f}s)")
        
        # Stage 2: Layout Analysis with Parallel Processing
        timing_metrics["Layout Analyzer"]["start"] = time.time()
        status_indicators["Layout Analyzer"].warning("🔄 Layout Analyzer\nAnalyzing chunks...")
        
        # Create semaphore for controlled concurrency
        max_concurrent = min(config.performance_config.get("max_concurrent_chunks", 5), len(processed_chunks))
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def analyze_chunk_with_semaphore(chunk):
            async with semaphore:
                return await layout_analyzer._analyze_chunk_layout(chunk)
        
        # Process chunks in parallel with controlled concurrency
        layout_tasks = [analyze_chunk_with_semaphore(chunk) for chunk in processed_chunks]
        layout_results = await asyncio.gather(*layout_tasks, return_exceptions=True)
        
        # Filter successful results
        layout_analysis = []
        for i, result in enumerate(layout_results):
            if isinstance(result, Exception):
                st.warning(f"Layout analysis failed for chunk {i+1}: {str(result)}")
            elif result:
                layout_analysis.append(result)
        
        timing_metrics["Layout Analyzer"]["end"] = time.time()
        
        if not layout_analysis:
            status_indicators["Layout Analyzer"].error("❌ Layout Analyzer\nFailed")
            st.error("Failed to analyze document layout")
            return None
        
        layout_time = timing_metrics["Layout Analyzer"]["end"] - timing_metrics["Layout Analyzer"]["start"]
        status_indicators["Layout Analyzer"].success(f"✅ Layout Analyzer\n{len(layout_analysis)} chunks ({layout_time:.1f}s)")
        
        # Stage 3: Data Extraction with Parallel Processing
        timing_metrics["Data Extractor"]["start"] = time.time()
        status_indicators["Data Extractor"].warning("🔄 Data Extractor\nExtracting data...")
        
        # Use same semaphore pattern for data extraction
        async def extract_chunk_with_semaphore(chunk):
            async with semaphore:
                return await data_extractor._extract_chunk_data(chunk)
        
        # Process chunks in parallel with controlled concurrency
        extraction_tasks = [extract_chunk_with_semaphore(chunk) for chunk in layout_analysis]
        extraction_results = await asyncio.gather(*extraction_tasks, return_exceptions=True)
        
        # Filter successful results
        extracted_data = []
        successful_extractions = 0
        for i, result in enumerate(extraction_results):
            if isinstance(result, Exception):
                st.warning(f"Data extraction failed for chunk {i+1}: {str(result)}")
            elif result:
                extracted_data.append(result)
                successful_extractions += 1
        
        timing_metrics["Data Extractor"]["end"] = time.time()
        
        if not extracted_data:
            status_indicators["Data Extractor"].error("❌ Data Extractor\nFailed")
            st.error("Failed to extract data from document")
            return None
        
        extract_time = timing_metrics["Data Extractor"]["end"] - timing_metrics["Data Extractor"]["start"]
        status_indicators["Data Extractor"].success(f"✅ Data Extractor\n{successful_extractions}/{len(layout_analysis)} chunks ({extract_time:.1f}s)")
        
        # Stage 4: Data Aggregation
        timing_metrics["Aggregator"]["start"] = time.time()
        status_indicators["Aggregator"].warning("🔄 Aggregator\nMerging data...")
        aggregated_data = await aggregator.aggregate_data(extracted_data)
        timing_metrics["Aggregator"]["end"] = time.time()
        
        if not aggregated_data:
            status_indicators["Aggregator"].error("❌ Aggregator\nFailed")
            st.error("Failed to aggregate extracted data")
            return None
        
        # Get aggregation summary
        agg_summary = aggregator.get_aggregation_summary(aggregated_data)
        holdings_count = len(aggregated_data.get('tabular_data', []))
        agg_time = timing_metrics["Aggregator"]["end"] - timing_metrics["Aggregator"]["start"]
        status_indicators["Aggregator"].success(f"✅ Aggregator\n{holdings_count} holdings ({agg_time:.1f}s)")
        
        # Stage 5: Validation
        timing_metrics["Validator"]["start"] = time.time()
        status_indicators["Validator"].warning("🔄 Validator\nValidating data...")
        validation_result = await validator.validate_data(aggregated_data)
        timing_metrics["Validator"]["end"] = time.time()
        
        validation_status = "Valid" if validation_result['is_valid'] else "Issues found"
        quality_score = validation_result.get('data_quality_score', 0.0)
        val_time = timing_metrics["Validator"]["end"] - timing_metrics["Validator"]["start"]
        
        if not validation_result['is_valid']:
            status_indicators["Validator"].warning(f"⚠️ Validator\n{validation_status} ({val_time:.1f}s)")
            st.warning(f"Data validation issues found: {len(validation_result.get('errors', []))} errors, {len(validation_result.get('warnings', []))} warnings")
        else:
            status_indicators["Validator"].success(f"✅ Validator\n{validation_status} ({val_time:.1f}s)")
        
        # Stage 6: Output Formatting
        timing_metrics["Output Formatter"]["start"] = time.time()
        status_indicators["Output Formatter"].warning("🔄 Output Formatter\nFormatting...")
        final_output = await output_formatter.format_output(
            aggregated_data, 
            validation_result
        )
        timing_metrics["Output Formatter"]["end"] = time.time()
        
        fmt_time = timing_metrics["Output Formatter"]["end"] - timing_metrics["Output Formatter"]["start"]
        status_indicators["Output Formatter"].success(f"✅ Output Formatter\nCompleted ({fmt_time:.1f}s)")
        
        # Show final processing summary
        with progress_container:
            st.success("🎉 Pipeline completed successfully!")
            
            # Calculate total processing time
            total_time = sum([
                timing_metrics[agent]["end"] - timing_metrics[agent]["start"] 
                for agent in timing_metrics if timing_metrics[agent]["start"] and timing_metrics[agent]["end"]
            ])
            
            # Processing metrics
            processing_info = final_output.get('_processing_info', {})
            col1, col2, col3, col4, col5 = st.columns(5)
            
            with col1:
                st.metric("Total Time", f"{total_time:.1f}s")
            with col2:
                st.metric("Chunks Processed", len(processed_chunks))
            with col3:
                st.metric("Holdings Extracted", processing_info.get('total_holdings_extracted', 0))
            with col4:
                st.metric("Quality Score", f"{quality_score:.1f}")
            with col5:
                st.metric("Completeness", f"{validation_result.get('completeness_score', 0.0):.1f}")
            
            # Detailed timing breakdown
            with st.expander("⏱️ Performance Breakdown"):
                timing_cols = st.columns(3)
                
                with timing_cols[0]:
                    st.write("**Agent Timing (seconds)**")
                    for agent, times in timing_metrics.items():
                        if times["start"] and times["end"]:
                            duration = times["end"] - times["start"]
                            st.write(f"• {agent}: {duration:.2f}s")
                
                with timing_cols[1]:
                    st.write("**Processing Stats**")
                    st.write(f"• Parallel chunks: {max_concurrent}")
                    st.write(f"• Layout success: {len(layout_analysis)}/{len(processed_chunks)}")
                    st.write(f"• Extraction success: {successful_extractions}/{len(layout_analysis)}")
                    st.write(f"• Validation errors: {len(validation_result.get('errors', []))}")
                
                with timing_cols[2]:
                    st.write("**Performance Metrics**")
                    avg_chunk_time = extract_time / len(layout_analysis) if layout_analysis else 0
                    st.write(f"• Avg chunk extraction: {avg_chunk_time:.2f}s")
                    st.write(f"• Processing speed: {len(processed_chunks)/total_time:.1f} chunks/s")
                    parallelization_benefit = (len(layout_analysis) * avg_chunk_time) / extract_time if extract_time > 0 else 1
                    st.write(f"• Parallel speedup: {parallelization_benefit:.1f}x")
        
        return final_output
        
    except Exception as e:
        st.error(f"Error in processing pipeline: {str(e)}")
        st.error(f"Traceback: {traceback.format_exc()}")
        return None

def display_extracted_data(data: Dict[str, Any]):
    """Display the extracted portfolio data"""
    if not data:
        st.error("No data to display")
        return
    
    st.success("✅ Document processing completed!")
    
    # Display main portfolio information
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📋 Portfolio Information")
        if 'document_name' in data:
            st.write(f"**Document:** {data['document_name']}")
        if 'advisor_name' in data:
            st.write(f"**Advisor:** {data['advisor_name']}")
        if 'client_name' in data:
            st.write(f"**Client:** {data['client_name']}")
        if 'portfolio_id' in data:
            st.write(f"**Portfolio ID:** {data['portfolio_id']}")
    
    with col2:
        st.subheader("💰 Summary")
        if 'total_account_value' in data:
            st.write(f"**Total Value:** {data['total_account_value']}")
        if 'analysis_date' in data:
            st.write(f"**Analysis Date:** {data['analysis_date']}")
    
    # Display tabular data
    if 'tabular_data' in data and data['tabular_data']:
        st.subheader("📊 Holdings Details")
        
        # Convert to display format
        holdings_data = []
        for item in data['tabular_data']:
            holdings_data.append([
                item.get('account_number', 'N/A'),
                item.get('ticker_symbol', 'N/A'),
                item.get('shares_quantity', 'N/A'),
                item.get('current_value', 'N/A'),
                item.get('cost_basis', 'N/A')
            ])
        
        if holdings_data:
            # Create table manually to avoid DataFrame column assignment issues
            header_cols = st.columns(5)
            headers = ['Account Number', 'Ticker Symbol', 'Shares', 'Current Value', 'Cost Basis']
            for i, header in enumerate(headers):
                header_cols[i].write(f"**{header}**")
            
            for row in holdings_data:
                data_cols = st.columns(5)
                for i, value in enumerate(row):
                    data_cols[i].write(str(value))
    
    # Display raw JSON
    with st.expander("🔍 Raw JSON Output"):
        st.json(data)
    
    # Download button
    json_str = json.dumps(data, indent=2)
    st.download_button(
        label="📥 Download JSON",
        data=json_str,
        file_name=f"portfolio_data_{data.get('portfolio_id', 'unknown')}.json",
        mime="application/json"
    )

def main():
    st.title("📊 Portfolio Document Extractor")
    st.markdown("Upload financial documents to extract structured portfolio data using AI agents")
    
    # Create two columns for the dual-panel layout
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.header("📁 Document Upload & Preview")
        
        # File uploader
        uploaded_file = st.file_uploader(
            "Choose a financial document",
            type=['pdf', 'doc', 'docx', 'png', 'jpg', 'jpeg', 'gif', 'bmp'],
            key=f"file_uploader_{st.session_state.upload_key}",
            help="Supported formats: PDF, Word documents, Images"
        )
        
        if uploaded_file is not None:
            # Reset processing state when new file is uploaded
            if not st.session_state.processing_complete:
                reset_session()
            
            # Display file preview
            st.subheader("👀 File Preview")
            display_file_preview(uploaded_file)
            
            # Process button
            if st.button("🚀 Process Document", type="primary"):
                with st.container():
                    # Run the async processing pipeline
                    try:
                        # Create event loop for async processing
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        result = loop.run_until_complete(process_document_pipeline(uploaded_file))
                        loop.close()
                        
                        if result:
                            st.session_state.extracted_data = result
                            st.session_state.processing_complete = True
                            st.rerun()
                        else:
                            st.error("Failed to process document. Please check the logs above.")
                            
                    except Exception as e:
                        st.error(f"Processing failed: {str(e)}")
                        st.error(f"Traceback: {traceback.format_exc()}")
        
        else:
            st.info("👆 Please upload a financial document to get started")
    
    with col2:
        st.header("📊 Extracted Portfolio Data")
        
        if st.session_state.processing_complete and st.session_state.extracted_data:
            display_extracted_data(st.session_state.extracted_data)
        else:
            st.info("📋 Extracted data will appear here after processing")
            
            # Show sample structure
            with st.expander("📖 Expected Output Structure"):
                sample_structure = {
                    "document_name": "Portfolio Statement Q3 2024",
                    "advisor_name": "John Smith Financial",
                    "client_name": "Jane Doe",
                    "portfolio_id": "PORT-12345",
                    "total_account_value": "$1,250,000",
                    "analysis_date": "2024-09-30",
                    "tabular_data": [
                        {
                            "account_number": "ACC-001",
                            "ticker_symbol": "AAPL",
                            "shares_quantity": "100",
                            "current_value": "$15,000",
                            "cost_basis": "$12,000"
                        }
                    ]
                }
                st.json(sample_structure)
    
    # Clear button
    if st.session_state.processing_complete:
        if st.button("🔄 Process New Document"):
            reset_session()
            st.rerun()

if __name__ == "__main__":
    main()
