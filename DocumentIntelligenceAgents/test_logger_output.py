#!/usr/bin/env python3
"""
Test script to demonstrate logger output issues
"""
import sys
import time
from utils.logger import <PERSON><PERSON><PERSON><PERSON>

def test_logger_output():
    """Test the logger output to identify visibility issues"""
    print("=== Testing AgentLogger Output ===")
    
    # Test 1: Basic logger creation
    print("\n--- Test 1: Creating Logger ---")
    logger = AgentLogger("TestAgent", log_level="INFO", use_streamlit=False)
    print("Logger created successfully")
    
    # Test 2: Basic logging methods
    print("\n--- Test 2: Basic Logging Methods ---")
    logger.info("This is an info message")
    logger.success("This is a success message")
    logger.warning("This is a warning message")
    logger.error("This is an error message")
    
    # Test 3: Operation logging
    print("\n--- Test 3: Operation Logging ---")
    logger.start_operation(
        "Test Operation", 
        "Testing the operation logging functionality",
        {"test_param": "test_value"}
    )
    
    time.sleep(1)
    
    logger.log_step("Step 1", {"detail": "Processing data"}, "IN_PROGRESS")
    time.sleep(0.5)
    
    logger.log_step("Step 2", {"detail": "Analyzing results"}, "COMPLETED")
    time.sleep(0.5)
    
    logger.complete_operation(success=True, summary={
        "steps_completed": 2,
        "total_time": "2.0s"
    })
    
    # Test 4: Metrics logging
    print("\n--- Test 4: Metrics Logging ---")
    logger.metrics({
        "processing_time": "2.5s",
        "items_processed": 100,
        "success_rate": "95%"
    })
    
    # Test 5: Check if output is visible
    print("\n--- Test 5: Output Visibility Check ---")
    print("If you can see this message, basic print() works")
    logger.info("If you can see this message, logger.info() works")
    
    # Test 6: Check logger configuration
    print("\n--- Test 6: Logger Configuration ---")
    print(f"Logger name: {logger.agent_name}")
    print(f"Logger level: {logger.logger.level}")
    print(f"Number of handlers: {len(logger.logger.handlers)}")
    
    for i, handler in enumerate(logger.logger.handlers):
        print(f"Handler {i}: {type(handler).__name__}")
        print(f"  Level: {handler.level}")
        print(f"  Stream: {handler.stream}")
    
    # Test 7: Direct logging calls
    print("\n--- Test 7: Direct Logging Calls ---")
    logger.logger.info("Direct logger.info() call")
    logger.logger.warning("Direct logger.warning() call")
    logger.logger.error("Direct logger.error() call")
    
    print("\n=== Test Complete ===")

if __name__ == "__main__":
    test_logger_output()
